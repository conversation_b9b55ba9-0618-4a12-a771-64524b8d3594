import "dotenv/config";
import fs from "fs";
import createError from "http-errors";
import { Client as rfcClient } from "node-rfc";
import { abapsystem } from "../auth/auth.js";
import joischema from "../schema/sapppschema.js";

export default class sapClient {
    // SAP Client
    #abapClient

    // Constructor Method
    constructor(req) {
        // create new sap-client 
        this.#abapClient = new rfcClient(abapsystem(req));
    }

    // Production Order Payload
    GetPPOrderPayload(reqQuery) {
        return new Promise(async (resolve, reject) => {
            const result = joischema.ProductionOrderPayload.validate(reqQuery);
            if (result?.error) {
                reject(createError(400, result?.error?.details[0]?.message));
            } else {
                const jsonpayload = JSON.parse(fs.readFileSync(`app/files/${result?.value?.Payload}.json`));
                resolve(jsonpayload);
            }
        })
    }

    PPOrderQueryValidation(query) {
        // Validate Query Parameter ORDERID
        return new Promise((resolve, reject) => {
            const result = joischema.ProductionOrderQuery.validate(query);
            if (result?.error) {
                reject(createError(400, result?.error?.details[0]?.message));
            } else {
                resolve(result?.value);
            }
        });
    }

    // Get Details of a Production Order
    bapi_prodord_get_details(reqQuery) {
        return new Promise(async (resolve, reject) => {
            try {
                const { OrderNumber } = reqQuery;
                await this.#abapClient.open();
                const retPPorder = await this.#abapClient.call("BAPI_PRODORD_GET_DETAIL", {
                    NUMBER: OrderNumber,
                    COLLECTIVE_ORDER: "",
                    ORDER_OBJECTS: {
                        HEADER: "X",
                        POSITIONS: "X",
                        SEQUENCES: "X",
                        OPERATIONS: "X",
                        COMPONENTS: "X",
                        PROD_REL_TOOLS: "X",
                        TRIGGER_POINTS: "X",
                        SUBOPERATIONS: "X"
                    }
                });
                await this.#abapClient.close();
                const {
                    HEADER: [Header],
                    POSITION: Items,
                    SEQUENCE: Sequences,
                    OPERATION: Operations,
                    TRIGGER_POINT: TriggerPoint,
                    COMPONENT: Component,
                    PROD_REL_TOOL: ResourcesTools
                } = retPPorder;

                resolve({ Header, Items, Sequences, Operations, TriggerPoint, Component, ResourcesTools });
            } catch (error) {
                reject(createError(400, error?.message))
            }
        })
    }

    PPOrderUpdateValidation(reqBody) {
        return new Promise((resolve, reject) => {
            const result = joischema.ProductionOrderUpdate.validate(reqBody);
            if (result?.error) {
                reject(createError(400, result?.error?.details[0]?.message));
            } else {
                resolve(result?.value);
            }
        });
    }

    bapi_prodord_update(PPOrder) {
        return new Promise(async (resolve, reject) => {
            try {
                let {
                    OrderNumber: IV_ORDER_NUMBER,
                    Header: IS_HEADER,
                    Sequences: IT_SEQUENCE,
                    Operations: IT_OPERATION,
                    ResourcesTools: IT_PRT
                } = PPOrder;

                if (!IS_HEADER) IS_HEADER = [];
                if (!IT_SEQUENCE) IT_SEQUENCE = [];
                if (!IT_OPERATION) IT_OPERATION = [];
                if (!IT_PRT) IT_PRT = [];

                await this.#abapClient.open();
                const retPPorder = await this.#abapClient.call("CO_SE_PRODORD_CHANGE", {
                    IV_ORDER_NUMBER,
                    IS_HEADER,
                    IT_SEQUENCE,
                    IT_OPERATION,
                    IT_PRT,
                    IV_COMMIT: "X" //Indicator: Perform Commit Work
                });
                await this.#abapClient.close();

                let { ES_RETURN: Return } = retPPorder;

                if (Return?.TYPE === "") { // BAPI Return is Empty when Success
                    Return.TYPE = "S";
                    Return.ID = "CO";
                    Return.NUMBER = "100";
                    Return.MESSAGE_V1 = IV_ORDER_NUMBER;
                    Return.MESSAGE = `Order number ${Return?.MESSAGE_V1} saved`;
                }
                
                resolve({ Return });
            } catch (error) {
                reject(createError(400, error?.message));
            }
        })
    }

}