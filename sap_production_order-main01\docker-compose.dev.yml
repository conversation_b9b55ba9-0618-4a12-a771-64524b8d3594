version: "3.9"
services:
  sap_production_order:
    container_name: sap_production_order
    build: .
    image: matw/sap_production_order
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 3G
        reservations:
          cpus: '0.25'
          memory: 2G
    ports:
      - "7230:7230"
    env_file:
      - "./config/config.env"
    environment:
      - PORT=7230
      - ASHOST=ud1-sv-saped1.matw.matthewsintl.com
      - SYSNR=00
      - CLIENT=310
      - LANGU=EN
      - ABAPSYS=ND1
      - RFC_TRACE=0
      - JWTLINK=http://c3mft01ld.amer.schawk.com:8082/auth/.well-known/jwks.json
      - JWTLOGIN=http://c3mft01ld.amer.schawk.com:8082/auth/login
    labels:
      - "co.elastic.logs/enabled=true"
      - "co.elastic.logs/fileset.stdout=access"
      - "co.elastic.logs/fileset.stderr=error"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"