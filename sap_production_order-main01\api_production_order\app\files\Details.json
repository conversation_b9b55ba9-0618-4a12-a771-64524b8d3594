{"Header": {"ORDER_NUMBER": "Order Number", "PRODUCTION_PLANT": "Plant", "MRP_CONTROLLER": "MRP controller for the order", "PRODUCTION_SCHEDULER": "Production Supervisor", "MATERIAL": "Material Number", "EXPL_DATE": "Date of BOM Explosion/Routing Transfer", "ROUTING_NO": "Routing number of operations in the order", "RESERVATION_NUMBER": "Number of Reservation/Dependent Requirement", "SCHED_RELEASE_DATE": "Scheduled release date", "ACTUAL_RELEASE_DATE": "Actual release date", "FINISH_DATE": "Basic finish date", "START_DATE": "Basic start date", "PRODUCTION_FINISH_DATE": "Scheduled finish", "PRODUCTION_START_DATE": "Scheduled start", "ACTUAL_START_DATE": "Actual start date", "ACTUAL_FINISH_DATE": "Actual finish date", "SCRAP": "Total scrap quantity in the order", "TARGET_QUANTITY": "Total order quantity", "UNIT": "Common unit of measure for all order items", "UNIT_ISO": "ISO code for unit of measurement", "PRIORITY": "Order priority", "ORDER_TYPE": "Order Type", "ENTERED_BY": "Entered by", "ENTER_DATE": "Created on", "DELETION_FLAG": "Deletion flag", "WBS_ELEMENT": "Work Breakdown Structure Element (WBS Element)", "CONF_NO": "Completion confirmation number for the operation", "CONF_CNT": "Internal counter", "INT_OBJ_NO": "Configuration (internal object number)", "SCHED_FIN_TIME": "Scheduled finish time", "SCHED_START_TIME": "Scheduled start time", "COLLECTIVE_ORDER": "Indicator: Order is part of collective order", "ORDER_SEQ_NO": "Seq. number order", "FINISH_TIME": "Basic finish (time)", "START_TIME": "Basic start time", "ACTUAL_START_TIME": "Actual start time", "LEADING_ORDER": "Leading order in current processing", "SALES_ORDER": "Sales Order Number", "SALES_ORDER_ITEM": "Item Number in Sales Order", "PROD_SCHED_PROFILE": "Production Scheduling Profile", "MATERIAL_TEXT": "Material description", "SYSTEM_STATUS": "System Status", "CONFIRMED_QUANTITY": "Committed quantity for order acc. to ATP check components", "PLAN_PLANT": "Planning Plant", "BATCH": "Batch Number", "MATERIAL_EXTERNAL": "Long Material Number for MATERIAL Field", "MATERIAL_GUID": "External GUID for MATERIAL Field", "MATERIAL_VERSION": "Version Number for MATERIAL Field"}, "Items": [{"ORDER_NUMBER": "Order Number", "ORDER_ITEM_NUMBER": "Order Item Number", "SALES_ORDER": "Sales order number", "SALES_ORDER_ITEM": "Sales order item", "SCRAP": "Scrap quantity in item", "QUANTITY": "Order item quantity", "DELIVERED_QUANTITY": "Quantity of goods received for the order item", "BASE_UNIT": "Base Unit of Measure", "BASE_UNIT_ISO": "ISO code for unit of measurement", "MATERIAL": "Material Number for Order", "ACTUAL_DELIVERY_DATE": "Actual delivery/finish date", "PLANNED_DELIVERY_DATE": "Delivery date from planned order", "PLAN_PLANT": "Planning plant for the order", "STORAGE_LOCATION": "Storage Location", "DELIVERY_COMPL": "\"Delivery Completed\" Indicator", "PRODUCTION_VERSION": "Production Version", "PROD_PLANT": "Plant", "ORDER_TYPE": "Order Type", "FINISH_DATE": "Basic finish date", "PRODUCTION_FINISH_DATE": "Scheduled finish", "BATCH": "Batch Number", "DELETION_FLAG": "Deletion flag", "MRP_AREA": "MRP Area", "MATERIAL_TEXT": "Material description", "MATERIAL_EXTERNAL": "Long Material Number for MATERIAL Field", "MATERIAL_GUID": "External GUID for MATERIAL Field", "MATERIAL_VERSION": "Version Number for MATERIAL Field"}], "Sequences": [{"ROUTING_NO": "Routing number of operations in the order", "COUNTER": "Internal counter", "TASK_LIST_TYPE": "Task List Type", "TASK_LIST_GROUP": "Key for Task List Group", "GROUP_COUNTER": "Group Counter", "SEQUENCE_NO": "Sequence", "SEQUENCE_CATEGORY": "Sequence category", "DESCRIPTION": "Sequence description", "LOT_SZ_MIN": "From lot size", "LOT_SZ_MAX": "To lot size", "BRANCH_OPERATION": "Number of the branch operation", "RETURN_OPERATION": "Number of the return operation", "ORDER_NUMBER": "Order Number", "TASK_MEASURE_UNIT": "Task list unit of measure", "TASK_MEASURE_UNIT_ISO": "ISO code for unit of measurement"}], "Operations": [{"ROUTING_NO": "Routing number of operations in the order", "COUNTER": "General counter for order", "SEQUENCE_NO": "Sequence", "CONF_NO": "Completion confirmation number for the operation", "CONF_CNT": "Confirmation counter", "PURCHASE_REQ_NO": "Purchase requisition number", "PURCHASE_REQ_ITEM": "Item number of the purchase requisition in the order", "GROUP_COUNTER": "Group Counter", "TASK_LIST_TYPE": "Task List Type", "TASK_LIST_GROUP": "Key for Task List Group", "OPERATION_NUMBER": "Operation/Activity Number", "OPR_CNTRL_KEY": "Control key", "PROD_PLANT": "Plant", "DESCRIPTION": "Operation short text", "DESCRIPTION2": "Second line of the description", "STANDARD_VALUE_KEY": "Standard value key", "ACTIVITY_TYPE_1": "Activity Type", "ACTIVITY_TYPE_2": "Activity Type", "ACTIVITY_TYPE_3": "Activity Type", "ACTIVITY_TYPE_4": "Activity Type", "ACTIVITY_TYPE_5": "Activity Type", "ACTIVITY_TYPE_6": "Activity Type", "UNIT": "Unit of Measure for Activity/Operation", "UNIT_ISO": "ISO code for unit of measurement", "QUANTITY": "Operation Quantity", "SCRAP": "Operation scrap", "EARL_SCHED_START_DATE_EXEC": "Earliest scheduled start: Execution (date)", "EARL_SCHED_START_TIME_EXEC": "Earliest scheduled start: Execution (time)", "EARL_SCHED_START_DATE_PROC": "Earliest scheduled start: Processing (date)", "EARL_SCHED_START_TIME_PROC": "Earliest scheduled start: Processing (time)", "EARL_SCHED_START_DATE_TEARD": "Earliest scheduled start: Teardown (date)", "EARL_SCHED_START_TIME_TEARD": "Earliest scheduled start: Teardown (time)", "EARL_SCHED_FIN_DATE_EXEC": "Earliest scheduled finish: Execution (date)", "EARL_SCHED_FIN_TIME_EXEC": "Earliest scheduled finish: Execution (time)", "LATE_SCHED_START_DATE_EXEC": "Latest scheduled start: Execution (date)", "LATE_SCHED_START_TIME_EXEC": "Latest scheduled start: Execution (time)", "LATE_SCHED_START_DATE_PROC": "Latest scheduled start: Processing (date)", "LATE_SCHED_START_TIME_PROC": "Latest scheduled start: Processing (time)", "LATE_SCHED_START_DATE_TEARD": "Latest scheduled start: Teardown (date)", "LATE_SCHED_START_TIME_TEARD": "Latest scheduled start: Teardown (time)", "LATE_SCHED_FIN_DATE_EXEC": "Latest scheduled finish: Execution (date)", "LATE_SCHED_FIN_TIME_EXEC": "Latest scheduled finish: Execution (time)", "WORK_CENTER": "Work center", "WORK_CENTER_TEXT": "Work center description", "SYSTEM_STATUS": "System Status", "SUBOPERATION": "Suboperation"}], "TriggerPoint": [{"MILESTONE_NUMBER": "Milestone number", "VALID_FROM_DATE": "Valid-From Date", "DELETION_INDICATOR": "Deletion Indicator", "CREATED_BY": "Milestone created by", "CREATED_ON": "Milestone created on", "CHANGED_BY": "Milestone changed by", "CHANGED_ON": "Milestone changed on", "STD_NET_WORK": "Standard network number", "STD_NET_TYPE": "Standard network type", "ALT_NET_WORK": "Alternative to a standard network", "IND_FUNCTIONS": "Indicator: Milestone is used for milestone functions", "DESCRIPTION": "Description", "OPERATION": "Operation/Activity Number", "ORDER_NUMBER": "Order Number"}], "Component": [{"RESERVATION_NUMBER": "Number of Reservation/Dependent Requirement", "RESERVATION_ITEM": "Item Number of Reservation/Dependent Requirement", "RESERVATION_TYPE": "Record type", "DELETION_INDICATOR": "Item is Deleted", "MATERIAL": "Material Number", "PROD_PLANT": "Plant", "STORAGE_LOCATION": "Storage Location", "SUPPLY_AREA": "Production Supply Area", "BATCH": "Batch Number", "SPECIAL_STOCK": "Special Stock Indicator", "REQ_DATE": "Requirement Date for the Component", "REQ_QUAN": "Requirement Quantity", "BASE_UOM": "Base Unit of Measure", "BASE_UOM_ISO": "ISO code for unit of measurement", "WITHDRAWN_QUANTITY": "Quantity Withdrawn", "ENTRY_QUANTITY": "Quantity in Unit of Entry", "ENTRY_UOM": "Unit of Entry", "ENTRY_UOM_ISO": "ISO code for unit of measurement", "ORDER_NUMBER": "Order Number", "MOVEMENT_TYPE": "Movement Type (Inventory Management)", "ITEM_CATEGORY": "Item Category (Bill of Material)", "ITEM_NUMBER": "BOM item number", "SEQUENCE": "Sequence", "OPERATION": "Operation/Activity Number", "BACKFLUSH": "Indicator: <PERSON><PERSON><PERSON><PERSON>", "VALUATION_SPEC_STOCK": "Valuation of Special Stock", "SYSTEM_STATUS": "Edited status text", "MATERIAL_DESCRIPTION": "Material Description (Short Text)", "COMMITED_QUANTITY": "Committed quantity", "SHORTAGE": "Shortfage", "PURCHASE_REQ_NO": "Purchase Requisition Number", "PURCHASE_REQ_ITEM": "Item Number of Purchase Requisition", "MATERIAL_EXTERNAL": "Long Material Number for MATERIAL Field", "MATERIAL_GUID": "External GUID for MATERIAL Field", "MATERIAL_VERSION": "Version Number for MATERIAL Field"}], "ResourcesTools": [{"ROUTING_NO": "Routing number of operations in the order", "COUNTER": "Internal counter", "PRT_ITEM_COUNT": "Item counter for production resources/tools", "DELETION_INDICATOR": "Deletion Indicator", "TASK_LIST_TYPE": "Task List Type", "TASK_LIST_GROUP": "Key for Task List Group", "CTRL_KEY": "Control key for management of production resources/tools", "EARL_SCHED_START_DATE": "Earliest scheduled start (date) for PRT usage", "EARL_SCHED_START_TIME": "Earliest scheduled start (time) for PRT usage", "EARL_SCHED_FINISH_DATE": "Earliest scheduled finish (date) of PRT usage", "EARL_SCHED_FINISH_TIME": "Earliest scheduled end (time) of PRT usage", "LATEST_SCHED_START_DATE": "Latest scheduled start (date) for PRT usage", "LATEST_SCHED_START_TIME": "Latest scheduled start (time) for PRT usage", "LATEST_SCHED_FINISH_DATE": "Latest scheduled finish (date) of PRT usage", "LATEST_SCHED_FINISH_TIME": "Latest scheduled end (time) for PRT usage", "ACTUAL_START_DATE": "Actual start (date) of PRT usage", "ACTUAL_START_TIME": "Actual start (time) of PRT usage", "ACTUAL_FINISH_DATE": "Actual finish (date) of PRT usage", "ACTUAL_FINISH_TIME": "Actual finish (time) of PRT usage", "DURATION": "Duration of PRT usage", "DURATION_UNIT": "Time unit for duration of PRT usage", "DURATION_UNIT_ISO": "ISO code for unit of measurement", "STD_VALUE_FOR_PRT_QTY": "Standard value for the PRT quantity", "STD_VALUE_UNIT": "Quantity unit of production resource/tool", "STD_VALUE_UNIT_ISO": "ISO code for unit of measurement", "TOTAL_QUANTITY": "Total planned quantity of production resource/tool", "QUANTITY_UNIT": "Quantity unit of production resource/tool", "QUANTITY_UNIT_ISO": "ISO code for unit of measurement", "FORMULA_TOT_QTY": "Formula for calculating the total quantity of PRT", "STD_USAGE_VALUE_FOR_PRT": "Standard usage value for production resources/tools", "STD_USAGE_VALUE_UNIT": "Usage value unit of the production resource/tool", "STD_USAGE_VALUE_UNIT_ISO": "ISO code for unit of measurement", "USAGE_VALUE": "Total planned usage value of production resource/tool", "USAGE_VALUE_UNIT": "Usage value unit of the production resource/tool", "USAGE_VALUE_UNIT_ISO": "ISO code for unit of measurement", "FORMULA_TOT_USAGE": "Formula for calculating the total usage value of PRT", "ORDER_NUMBER": "Order Number", "PRT_CATEGORY": "Production resources/tools category", "PRT_NUMBER": "Production resource/tool number", "PRT_PLANT": "Production resources/tools in plant", "MATERIAL": "Material Number", "DOCUMENT_TYPE": "Document Type", "DOCUMENT_NUMBER": "Document number", "DOCUMENT_VERSION": "Document Version", "DOCUMENT_PART": "Document Part", "EQUIPMENT": "Equipment Number", "MEASURING_POINT": "Measuring Point", "DESCRIPTION_MEASURING_POINT": "Description of Measuring Point", "DESCRIPTION": "Description of the production resource/tool", "BASE_UNIT": "Base unit of measure for production resources/tools", "BASE_UNIT_ISO": "ISO code for unit of measurement", "SYSTEM_STATUS": "System Status", "MATERIAL_EXTERNAL": "Long Material Number for MATERIAL Field", "MATERIAL_GUID": "External GUID for MATERIAL Field", "MATERIAL_VERSION": "Version Number for MATERIAL Field"}]}