import "dotenv/config";
import { expressjwt } from "express-jwt";
import JwksRsa from "jwks-rsa";
import { Client as rfcClient } from "node-rfc";
import createError from "http-errors";

const abapsystem = (req) => {

    const targetsys = {
        DEST: "MME",
        ashost: process.env.ASHOST,
        sysnr: process.env.SYSNR,
        client: process.env.CLIENT,
        lang: process.env.LANGU,
    };

    if (req?.matwtoken) {
        S
        targetsys.user = process.env[`SAPUSER${process.env.ABAPYS}`];
        targetsys.passwd = process.env[`SAPPSW${process.env.ABAPSYS}`];
    } else {
        const credentials = new Buffer.from(req.headers.authorization.split(" ")[1], 'base64').toString();
        if (credentials) {
            targetsys.user = credentials.split(":")[0];
            targetsys.passwd = credentials.split(":")[1];
        }
    }

    return targetsys;
}

const extractTokenFromHeader = (req) => {
    if (req?.cookies?.MATWGLTOKEN) {
        return req?.cookies?.MATWGLTOKEN;
    } else if (req?.headers?.authorization && req?.headers?.authorization.split(" ")[0] === "Bearer") {
        return req?.headers?.authorization.split(" ")[1];
    }
    return null;
}

const verifytoken = (req, res, next) => {
    if (req?.headers?.authorization && req?.headers?.authorization.split(" ")[0] === "Basic") {
        //create new sap-client for user authentication
        const abapClient = new rfcClient(abapsystem(req));
        abapClient.open().then(res => next()).catch(error => next(createError(401, error?.message)));
    } else {
        return expressjwt({ //Token Based Authentication
            secret: JwksRsa.expressJwtSecret({
                cache: true,
                rateLimit: true,
                jwksRequestsPerMinute: 5,
                jwksUri: process.env.JWTLINK,
            }),
            getToken: extractTokenFromHeader,
            requestProperty: "matwtoken",
            algorithms: ["RS256"],
        }).unless({})(req, res, next);
    }
};

export { verifytoken, abapsystem };