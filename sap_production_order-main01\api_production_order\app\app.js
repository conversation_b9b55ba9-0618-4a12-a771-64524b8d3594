import "dotenv/config";
import express from 'express';
import session from 'express-session';
import cookieParser from 'cookie-parser';
import logger from 'morgan';
import helmet from 'helmet';
import createError from "http-errors";
import router from './routes/route.js';

const app = express();

// all environments
app.disable("x-powered-by");
app.use(helmet());
app.use(logger("dev"));
app.use(
    session({
        secret: process.env.APISECRET,
        saveUninitialized: true,
        resave: true
    }));
app.use(cookieParser());
app.use(express.json());
app.use(express.urlencoded({
    extended: true
}));

app.use('/SAP/ProductionOrder', router);

/* When any Invalid Path Provided. */
app.use((req, res, next) => {
    next(createError(404, `Path '${req.path}' Not found`));
});

/* An error handling middleware. */
app.use((error, req, res, next) => {
    res.status(error.status || 500).send({ Error: error.message });
});

export default app;