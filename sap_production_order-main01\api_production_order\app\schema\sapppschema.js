/* Module dependencies */
import JoiBase from "joi";
import JoiDate from "@joi/date";

const Joi = JoiBase.extend(JoiDate);

const JoisetX = {
    not: null,
    then: Joi.string().default("X"),
    otherwise: Joi.string().empty(null).empty(null, "")
}

const joischema = {

    ProductionOrderPayload: Joi.object().keys({
        Payload: Joi.string().min(6).max(7).valid("Details", "Update", "Create").required()
    }),

    ProductionOrderQuery: Joi.object().keys({
        OrderNumber: Joi.string().min(12).max(12).required()
    }),

    ProductionOrderUpdate: Joi.object().keys({
        OrderNumber: Joi.string().min(12).max(12).required(),
        Header: Joi.object().keys({
            BASIC_START_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).allow(null, ""),
            BASIC_START_DATE_X: Joi.string().max(1).valid("X").when("BASIC_START_DATE", JoisetX),
            BASIC_START_TIME: Joi.date().format("HHmmss").raw().empty(null).allow(null, ""),
            BASIC_END_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).allow(null, ""),
            BASIC_END_DATE_X: Joi.string().max(1).valid("X").when("BASIC_END_DATE", JoisetX),
            BASIC_END_TIME: Joi.date().format("HHmmss").raw().empty(null).allow(null, ""),
            QUANTITY: Joi.string().max(17).empty(null).allow(null, ""),
            QUANTITY_X: Joi.string().max(1).valid("X").when("QUANTITY", JoisetX),
            SCRAP_QUANTITY: Joi.string().max(17).empty(null).allow(null, ""),
            SCRAP_QUANTITY_X: Joi.string().max(1).valid("X").when("SCRAP_QUANTITY", JoisetX),
            QUANTITY_UOM: Joi.string().max(3).empty(null).allow(null, ""),
            QUANTITY_UOM_X: Joi.string().max(1).valid("X").when("QUANTITY_UOM", JoisetX),
            ROUTING_TYPE: Joi.string().max(1).empty(null).allow(null, ""),
            ROUTING_GROUP: Joi.string().max(8).empty(null).allow(null, ""),
            ROUTING_COUNTER: Joi.string().max(2).empty(null).allow(null, ""),
            ROUTING_X: Joi.string().max(1).valid("X").when("ROUTING", JoisetX),
            PROD_VERSION: Joi.string().max(4).empty(null).allow(null, ""),
            PROD_VERSION_X: Joi.string().max(1).valid("X").when("PROD_VERSION", JoisetX),
            EXPLOSION_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            EXPLOSION_DATE_X: Joi.string().max(1).valid("X").when("EXPLOSION_DATE", JoisetX),
            ORDER_PRIORITY: Joi.string().max(1).empty(null).allow(null, ""),
            ORDER_PRIORITY_X: Joi.string().max(1).valid("X").when("ORDER_PRIORITY", JoisetX),
            MRP_CONTROLLER: Joi.string().max(3).empty(null).allow(null, ""),
            MRP_CONTROLLER_X: Joi.string().max(1).valid("X").when("MRP_CONTROLLER", JoisetX),
            EXPLODE_NEW: Joi.string().max(1).empty(null).allow(null, ""),
            BUSINESS_AREA: Joi.string().max(4).empty(null).allow(null, ""),
            BUSINESS_AREA_X: Joi.string().max(1).valid("X").when("BUSINESS_AREA", JoisetX),
            PROFIT_CENTER: Joi.string().max(10).empty(null).allow(null, ""),
            PROFIT_CENTER_X: Joi.string().max(1).valid("X").when("PROFIT_CENTER", JoisetX),
            SEQ_NUMBER: Joi.string().max(14).empty(null).allow(null, ""),
            SEQ_NUMBER_X: Joi.string().max(1).valid("X").when("SEQ_NUMBER", JoisetX),
            STOCK_TYPE: Joi.string().max(1).empty(null).allow(null, ""),
            STOCK_TYPE_X: Joi.string().max(1).valid("X").when("STOCK_TYPE", JoisetX),
            GR_PROC_TIME: Joi.string().max(3).empty(null).allow(null, ""),
            GR_PROC_TIME_X: Joi.string().max(1).valid("X").when("GR_PROC_TIME", JoisetX),
            STORAGE_LOCATION: Joi.string().max(4).empty(null).allow(null, ""),
            STORAGE_LOCATION_X: Joi.string().max(1).valid("X").when("STORAGE_LOCATION", JoisetX),
            MRP_DISTR_KEY: Joi.string().max(4).empty(null).allow(null, ""),
            MRP_DISTR_KEY_X: Joi.string().max(1).valid("X").when("MRP_DISTR_KEY", JoisetX),
            GOODS_RECIPIENT: Joi.string().max(12).empty(null).allow(null, ""),
            GOODS_RECIPIENT_X: Joi.string().max(1).valid("X").when("GOODS_RECIPIENT", JoisetX),
            UNLOADING_POINT: Joi.string().max(25).empty(null).allow(null, ""),
            UNLOADING_POINT_X: Joi.string().max(1).valid("X").when("UNLOADING_POINT", JoisetX),
            POSITION_NUMBER: Joi.string().max(4).empty(null).allow(null, "")
        }),
        Sequences: Joi.array().items({
            SEQUENCE: Joi.string().max(6).empty(null).empty(null, ""),
            BRANCH_OPER: Joi.string().max(4).empty(null).empty(null, ""),
            BRANCH_OPER_X: Joi.string().max(1).valid("X").when("BRANCH_OPER", JoisetX),
            RETURN_OPER: Joi.string().max(4).empty(null).empty(null, ""),
            RETURN_OPER_X: Joi.string().max(1).valid("X").when("RETURN_OPER", JoisetX),
            TEXT: Joi.string().max(40).empty(null).empty(null, ""),
            TEXT_X: Joi.string().max(1).valid("X").when("TEXT", JoisetX),
            ALIGN: Joi.string().max(1).empty(null).empty(null, ""),
            ALIGN_X: Joi.string().max(1).valid("X").when("ALIGN", JoisetX),
            DELETION_IND: Joi.string().max(1).empty(null).empty(null, "")
        }),
        Operations: Joi.array().items({
            SEQUENCE: Joi.string().max(6).empty(null).empty(null, ""),
            OPERATION: Joi.string().max(4).empty(null).empty(null, ""),
            SUB_OPERATION: Joi.string().max(4).empty(null).empty(null, ""),
            DELETION_IND: Joi.string().max(1).empty(null).empty(null, ""),
            CONTROL_KEY: Joi.string().max(4).empty(null).empty(null, ""),
            WORK_CENTER: Joi.string().max(8).empty(null).empty(null, ""),
            WORK_CENTER_X: Joi.string().max(1).valid("X").when("WORK_CENTER", JoisetX),
            SHORT_TEXT: Joi.string().max(40).empty(null).empty(null, ""),
            SHORT_TEXT_X: Joi.string().max(1).valid("X").when("SHORT_TEXT", JoisetX),
            SEQ_NUMBER: Joi.string().max(14).empty(null).empty(null, ""),
            SEQ_NUMBER_X: Joi.string().max(1).valid("X").when("SEQ_NUMBER", JoisetX),
            BASE_QUANTITY: Joi.string().max(17).empty(null).empty(null, ""),
            BASE_QUANTITY_X: Joi.string().max(1).valid("X").when("BASE_QUANTITY", JoisetX),
            STANDARD_VALUE_01: Joi.string().max(11).empty(null).empty(null, ""),
            STANDARD_VALUE_01_X: Joi.string().max(1).valid("X").when("STANDARD_VALUE_01", JoisetX),
            STANDARD_VALUE_01_UNIT: Joi.string().max(3).empty(null).empty(null, ""),
            STANDARD_VALUE_01_UNIT_X: Joi.string().max(1).valid("X").when("STANDARD_VALUE_01_UNIT", JoisetX),
            STANDARD_VALUE_02: Joi.string().max(11).empty(null).empty(null, ""),
            STANDARD_VALUE_02_X: Joi.string().max(1).valid("X").when("STANDARD_VALUE_02", JoisetX),
            STANDARD_VALUE_02_UNIT: Joi.string().max(3).empty(null).empty(null, ""),
            STANDARD_VALUE_02_UNIT_X: Joi.string().max(1).valid("X").when("STANDARD_VALUE_02_UNIT", JoisetX),
            STANDARD_VALUE_03: Joi.string().max(11).empty(null).empty(null, ""),
            STANDARD_VALUE_03_X: Joi.string().max(1).valid("X").when("STANDARD_VALUE_03", JoisetX),
            STANDARD_VALUE_03_UNIT: Joi.string().max(3).empty(null).empty(null, ""),
            STANDARD_VALUE_03_UNIT_X: Joi.string().max(1).valid("X").when("STANDARD_VALUE_03_UNIT", JoisetX),
            STANDARD_VALUE_04: Joi.string().max(11).empty(null).empty(null, ""),
            STANDARD_VALUE_04_X: Joi.string().max(1).valid("X").when("STANDARD_VALUE_04", JoisetX),
            STANDARD_VALUE_04_UNIT: Joi.string().max(3).empty(null).empty(null, ""),
            STANDARD_VALUE_04_UNIT_X: Joi.string().max(1).valid("X").when("STANDARD_VALUE_04_UNIT", JoisetX),
            STANDARD_VALUE_05: Joi.string().max(11).empty(null).empty(null, ""),
            STANDARD_VALUE_05_X: Joi.string().max(1).valid("X").when("STANDARD_VALUE_05", JoisetX),
            STANDARD_VALUE_05_UNIT: Joi.string().max(3).empty(null).empty(null, ""),
            STANDARD_VALUE_05_UNIT_X: Joi.string().max(1).valid("X").when("STANDARD_VALUE_05_UNIT", JoisetX),
            STANDARD_VALUE_06: Joi.string().max(11).empty(null).empty(null, ""),
            STANDARD_VALUE_06_X: Joi.string().max(1).valid("X").when("STANDARD_VALUE_06", JoisetX),
            STANDARD_VALUE_06_UNIT: Joi.string().max(3).empty(null).empty(null, ""),
            STANDARD_VALUE_06_UNIT_X: Joi.string().max(1).valid("X").when("STANDARD_VALUE_06_UNIT", JoisetX),
            SPLIT_REQUIRED_INDICATOR: Joi.string().max(1).empty(null).empty(null, ""),
            SPLIT_REQUIRED_INDICATOR_X: Joi.string().max(1).valid("X").when("SPLIT_REQUIRED_INDICATOR", JoisetX),
            SPLIT_MAX_NUMBER: Joi.string().max(3).empty(null).empty(null, ""),
            SPLIT_MAX_NUMBER_X: Joi.string().max(1).valid("X").when("SPLIT_MAX_NUMBER", JoisetX),
            SPLIT_MIN_PROC_TIME: Joi.string().max(11).empty(null).empty(null, ""),
            SPLIT_MIN_PROC_TIME_X: Joi.string().max(1).valid("X").when("SPLIT_MIN_PROC_TIME", JoisetX),
            SPLIT_MIN_PROC_TIME_UNIT: Joi.string().max(3).empty(null).empty(null, ""),
            SPLIT_MIN_PROC_TIME_UNIT_X: Joi.string().max(1).valid("X").when("SPLIT_MIN_PROC_TIME_UNIT", JoisetX),
            OVERLAP_REQUIRED: Joi.string().max(1).empty(null).empty(null, ""),
            OVERLAP_REQUIRED_X: Joi.string().max(1).valid("X").when("OVERLAP_REQUIRED", JoisetX),
            OVERLAP_OPTIONAL: Joi.string().max(1).empty(null).empty(null, ""),
            OVERLAP_OPTIONAL_X: Joi.string().max(1).valid("X").when("OVERLAP_OPTIONAL", JoisetX),
            OVERLAP_CONT_FLOW: Joi.string().max(1).empty(null).empty(null, ""),
            OVERLAP_CONT_FLOW_X: Joi.string().max(1).valid("X").when("OVERLAP_CONT_FLOW", JoisetX),
            OVERLAP_MIN_TIME: Joi.string().max(11).empty(null).empty(null, ""),
            OVERLAP_MIN_TIME_X: Joi.string().max(1).valid("X").when("OVERLAP_MIN_TIME", JoisetX),
            OVERLAP_MIN_TIME_UNIT: Joi.string().max(3).empty(null).empty(null, ""),
            OVERLAP_MIN_TIME_UNIT_X: Joi.string().max(1).valid("X").when("OVERLAP_MIN_TIME_UNIT", JoisetX),
            SEND_AHEAD_MIN_QUANTITY: Joi.string().max(17).empty(null).empty(null, ""),
            SEND_AHEAD_MIN_QUANTITY_X: Joi.string().max(1).valid("X").when("SEND_AHEAD_MIN_QUANTITY", JoisetX),
            PURCHASING_GROUP: Joi.string().max(3).empty(null).empty(null, ""),
            PURCHASING_GROUP_X: Joi.string().max(1).valid("X").when("PURCHASING_GROUP", JoisetX),
            MATERIAL_GROUP: Joi.string().max(9).empty(null).empty(null, ""),
            MATERIAL_GROUP_X: Joi.string().max(1).valid("X").when("MATERIAL_GROUP", JoisetX),
            LATEST_END_EXEC_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            LATEST_END_EXEC_DATE_X: Joi.string().max(1).valid("X").when("LATEST_END_EXEC_DATE", JoisetX),
            LATEST_END_EXEC_TIME: Joi.date().format("HHmmss").raw().empty(null).empty(null, ""),
            LATEST_END_EXEC_TIME_X: Joi.string().max(1).valid("X").when("LATEST_END_EXEC_TIME", JoisetX),
            LATEST_START_EXEC_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            LATEST_START_EXEC_DATE_X: Joi.string().max(1).valid("X").when("LATEST_START_EXEC_DATE", JoisetX),
            LATEST_START_EXEC_TIME: Joi.date().format("HHmmss").raw().empty(null).empty(null, ""),
            LATEST_START_EXEC_TIME_X: Joi.string().max(1).valid("X").when("LATEST_START_EXEC_TIME", JoisetX),
            LATEST_START_PROC_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            LATEST_START_PROC_DATE_X: Joi.string().max(1).valid("X").when("LATEST_START_PROC_DATE", JoisetX),
            LATEST_START_PROC_TIME: Joi.date().format("HHmmss").raw().empty(null).empty(null, ""),
            LATEST_START_PROC_TIME_X: Joi.string().max(1).valid("X").when("LATEST_START_PROC_TIME", JoisetX),
            LATEST_START_TEARDOWN_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            LATEST_START_TEARDOWN_DATE_X: Joi.string().max(1).valid("X").when("LATEST_START_TEARDOWN_DATE", JoisetX),
            LATEST_START_TEARDOWN_TIME: Joi.date().format("HHmmss").raw().empty(null).empty(null, ""),
            LATEST_START_TEARDOWN_TIME_X: Joi.string().max(1).valid("X").when("LATEST_START_TEARDOWN_TIME", JoisetX),
            LATEST_START_WAIT_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            LATEST_START_WAIT_DATE_X: Joi.string().max(1).valid("X").when("LATEST_START_WAIT_DATE", JoisetX),
            LATEST_START_WAIT_TIME: Joi.date().format("HHmmss").raw().empty(null).empty(null, ""),
            LATEST_START_WAIT_TIME_X: Joi.string().max(1).valid("X").when("LATEST_START_WAIT_TIME", JoisetX),
            LATEST_END_WAIT_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            LATEST_END_WAIT_DATE_X: Joi.string().max(1).valid("X").when("LATEST_END_WAIT_DATE", JoisetX),
            LATEST_END_WAIT_TIME: Joi.date().format("HHmmss").raw().empty(null).empty(null, ""),
            LATEST_END_WAIT_TIME_X: Joi.string().max(1).valid("X").when("LATEST_END_WAIT_TIME", JoisetX),
            EARLIEST_END_EXEC_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            EARLIEST_END_EXEC_DATE_X: Joi.string().max(1).valid("X").when("EARLIEST_END_EXEC_DATE", JoisetX),
            EARLIEST_END_EXEC_TIME: Joi.date().format("HHmmss").raw().empty(null).empty(null, ""),
            EARLIEST_END_EXEC_TIME_X: Joi.string().max(1).valid("X").when("EARLIEST_END_EXEC_TIME", JoisetX),
            EARLIEST_START_EXEC_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            EARLIEST_START_EXEC_DATE_X: Joi.string().max(1).valid("X").when("EARLIEST_START_EXEC_DATE", JoisetX),
            EARLIEST_START_EXEC_TIME: Joi.date().format("HHmmss").raw().empty(null).empty(null, ""),
            EARLIEST_START_EXEC_TIME_X: Joi.string().max(1).valid("X").when("EARLIEST_START_EXEC_TIME", JoisetX),
            EARLIEST_START_PROC_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            EARLIEST_START_PROC_DATE_X: Joi.string().max(1).valid("X").when("EARLIEST_START_PROC_DATE", JoisetX),
            EARLIEST_START_PROC_TIME: Joi.date().format("HHmmss").raw().empty(null).empty(null, ""),
            EARLIEST_START_PROC_TIME_X: Joi.string().max(1).valid("X").when("EARLIEST_START_PROC_TIME", JoisetX),
            EARLIEST_START_TEARDOWN_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            EARLIEST_START_TEARDOWN_DATE_X: Joi.string().max(1).valid("X").when("EARLIEST_START_TEARDOWN_DATE", JoisetX),
            EARLIEST_START_TEARDOWN_TIME: Joi.date().format("HHmmss").raw().empty(null).empty(null, ""),
            EARLIEST_START_TEARDOWN_TIME_X: Joi.string().max(1).valid("X").when("EARLIEST_START_TEARDOWN_TIME", JoisetX),
            EARLIEST_START_WAIT_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            EARLIEST_START_WAIT_DATE_X: Joi.string().max(1).valid("X").when("EARLIEST_START_WAIT_DATE", JoisetX),
            EARLIEST_START_WAIT_TIME: Joi.date().format("HHmmss").raw().empty(null).empty(null, ""),
            EARLIEST_START_WAIT_TIME_X: Joi.string().max(1).valid("X").when("EARLIEST_START_WAIT_TIME", JoisetX),
            EARLIEST_END_WAIT_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            EARLIEST_END_WAIT_DATE_X: Joi.string().max(1).valid("X").when("EARLIEST_END_WAIT_DATE", JoisetX),
            EARLIEST_END_WAIT_TIME: Joi.date().format("HHmmss").raw().empty(null).empty(null, ""),
            EARLIEST_END_WAIT_TIME_X: Joi.string().max(1).valid("X").when("EARLIEST_END_WAIT_TIME", JoisetX),
            USER_DEFINED_KEY: Joi.string().max(7).empty(null).empty(null, ""),
            USER_DEFINED_KEY_X: Joi.string().max(1).valid("X").when("USER_DEFINED_KEY", JoisetX),
            USER_FIELD_00_CHARACTER: Joi.string().max(20).empty(null).empty(null, ""),
            USER_FIELD_00_CHARACTER_X: Joi.string().max(1).valid("X").when("USER_FIELD_00_CHARACTER", JoisetX),
            USER_FIELD_01_CHARACTER: Joi.string().max(20).empty(null).empty(null, ""),
            USER_FIELD_01_CHARACTER_X: Joi.string().max(1).valid("X").when("USER_FIELD_01_CHARACTER", JoisetX),
            USER_FIELD_02_CHARACTER: Joi.string().max(10).empty(null).empty(null, ""),
            USER_FIELD_02_CHARACTER_X: Joi.string().max(1).valid("X").when("USER_FIELD_02_CHARACTER", JoisetX),
            USER_FIELD_03_CHARACTER: Joi.string().max(10).empty(null).empty(null, ""),
            USER_FIELD_03_CHARACTER_X: Joi.string().max(1).valid("X").when("USER_FIELD_03_CHARACTER", JoisetX),
            USER_FIELD_04_QUANTITY: Joi.string().max(18).empty(null).empty(null, ""),
            USER_FIELD_04_QUANTITY_X: Joi.string().max(1).valid("X").when("USER_FIELD_04_QUANTITY", JoisetX),
            USER_FIELD_04_UNIT: Joi.string().max(3).empty(null).empty(null, ""),
            USER_FIELD_04_UNIT_X: Joi.string().max(1).valid("X").when("USER_FIELD_04_UNIT", JoisetX),
            USER_FIELD_05_QUANTITY: Joi.string().max(18).empty(null).empty(null, ""),
            USER_FIELD_05_QUANTITY_X: Joi.string().max(1).valid("X").when("USER_FIELD_05_QUANTITY", JoisetX),
            USER_FIELD_05_UNIT: Joi.string().max(3).empty(null).empty(null, ""),
            USER_FIELD_05_UNIT_X: Joi.string().max(1).valid("X").when("USER_FIELD_05_UNIT", JoisetX),
            USER_FIELD_06_QUANTITY: Joi.string().max(18).empty(null).empty(null, ""),
            USER_FIELD_06_QUANTITY_X: Joi.string().max(1).valid("X").when("USER_FIELD_06_QUANTITY", JoisetX),
            USER_FIELD_06_UNIT: Joi.string().max(5).empty(null).empty(null, ""),
            USER_FIELD_06_UNIT_X: Joi.string().max(1).valid("X").when("USER_FIELD_06_UNIT", JoisetX),
            USER_FIELD_07_QUANTITY: Joi.string().max(18).empty(null).empty(null, ""),
            USER_FIELD_07_QUANTITY_X: Joi.string().max(1).valid("X").when("USER_FIELD_07_QUANTITY", JoisetX),
            USER_FIELD_07_UNIT: Joi.string().max(5).empty(null).empty(null, ""),
            USER_FIELD_07_UNIT_X: Joi.string().max(1).valid("X").when("USER_FIELD_07_UNIT", JoisetX),
            USER_FIELD_08_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            USER_FIELD_08_DATE_X: Joi.string().max(1).valid("X").when("USER_FIELD_08_DATE", JoisetX),
            USER_FIELD_09_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(null, ""),
            USER_FIELD_09_DATE_X: Joi.string().max(1).valid("X").when("USER_FIELD_09_DATE", JoisetX),
            USER_FIELD_10_INDICATOR: Joi.string().max(1).empty(null).empty(null, ""),
            USER_FIELD_10_INDICATOR_X: Joi.string().max(1).valid("X").when("USER_FIELD_10_INDICATOR", JoisetX),
            USER_FIELD_11_INDICATOR: Joi.string().max(1).empty(null).empty(null, ""),
            USER_FILED_11_INDICATOR_X: Joi.string().max(1).valid("X").when("USER_FILED_11_INDICATOR", JoisetX),
            DISPATCH_INDICATOR: Joi.string().max(1).empty(null).empty(null, ""),
            DEALLOCATE_INDICATOR: Joi.string().max(1).empty(null).empty(null, "")
        }),
        ResourcesTools: Joi.array().items({
            SEQUENCE: Joi.string().max(6).empty(null).empty(null, ""),
            OPERATION: Joi.string().max(4).empty(null).empty(null, ""),
            PRT_ITEM_NO: Joi.string().max(4).empty(null).empty(null, ""),
            PRT_CATEGORY: Joi.string().max(1).empty(null).empty(null, ""),
            PLANT: Joi.string().max(4).empty(null).empty(null, ""),
            MATERIAL: Joi.string().max(18).empty(null).empty(null, ""),
            MISC_PRT: Joi.string().max(18).empty(null).empty(null, ""),
            DOCUMENT_NO: Joi.string().max(25).empty(null).empty(null, ""),
            DOCUMENT_TYPE: Joi.string().max(3).empty(null).empty(null, ""),
            DOCUMENT_PART: Joi.string().max(3).empty(null).empty(null, ""),
            DOCUMENT_VERSION: Joi.string().max(2).empty(null).empty(null, ""),
            EQUIPMENT: Joi.string().max(18).empty(null).empty(null, ""),
            STEUF: Joi.string().max(4).empty(null).empty(null, ""),
            TEXT: Joi.string().max(40).empty(null).empty(null, ""),
            TEXT_X: Joi.string().max(1).valid("X").when("TEXT", JoisetX),
            QUANTITY: Joi.string().max(11).empty(null).empty(null, ""),
            QUANTITY_X: Joi.string().max(1).valid("X").when("QUANTITY", JoisetX),
            QUANTITY_UOM: Joi.string().max(3).empty(null).empty(null, ""),
            QUANTITY_UOM_X: Joi.string().max(1).valid("X").when("QUANTITY_UOM", JoisetX),
            QUANTITY_FORMULA: Joi.string().max(6).empty(null).empty(null, ""),
            QUANTITY_FORMULA_X: Joi.string().max(1).valid("X").when("QUANTITY_FORMULA", JoisetX),
            USAGE_VALUE: Joi.string().max(11).empty(null).empty(null, ""),
            USAGE_VALUE_X: Joi.string().max(1).valid("X").when("USAGE_VALUE", JoisetX),
            USAGE_VALUE_UOM: Joi.string().max(3).empty(null).empty(null, ""),
            USAGE_VALUE_UOM_X: Joi.string().max(1).valid("X").when("USAGE_VALUE_UOM", JoisetX),
            USAGE_VALUE_FORMULA: Joi.string().max(6).empty(null).empty(null, ""),
            USAGE_VALUE_FORMULA_X: Joi.string().max(1).valid("X").when("USAGE_VALUE_FORMULA", JoisetX),
            START_OFFSET: Joi.string().max(7).empty(null).empty(null, ""),
            START_OFFSET_X: Joi.string().max(1).valid("X").when("START_OFFSET", JoisetX),
            START_OFFSET_UOM: Joi.string().max(3).empty(null).empty(null, ""),
            START_OFFSET_UOM_X: Joi.string().max(1).valid("X").when("START_OFFSET_UOM", JoisetX),
            START_REF_DATE: Joi.string().max(2).empty(null).empty(null, ""),
            START_REF_DATE_X: Joi.string().max(1).valid("X").when("START_REF_DATE", JoisetX),
            END_OFFSET: Joi.string().max(7).empty(null).empty(null, ""),
            END_OFFSET_X: Joi.string().max(1).valid("X").when("END_OFFSET", JoisetX),
            END_OFFSET_UOM: Joi.string().max(3).empty(null).empty(null, ""),
            END_OFFSET_UOM_X: Joi.string().max(1).valid("X").when("END_OFFSET_UOM", JoisetX),
            END_REF_DATE: Joi.string().max(2).empty(null).empty(null, ""),
            END_REF_DATE_X: Joi.string().max(1).valid("X").when("END_REF_DATE", JoisetX),
            DELETION_IND: Joi.string().max(1).empty(null).empty(null, "")
        })
    })

}

export default joischema;