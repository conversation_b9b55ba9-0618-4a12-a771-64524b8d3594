{"OrderNumber": "Production Order number", "Header": {"BASIC_START_DATE": "Basic start date", "BASIC_START_TIME": "Basic start time", "BASIC_END_DATE": "Basic finish date", "BASIC_END_TIME": "Basic finish (time)", "QUANTITY": "Total order quantity", "SCRAP_QUANTITY": "Total scrap quantity in the order", "QUANTITY_UOM": "Common unit of measure for all order items", "ROUTING_TYPE": "Task List Type", "ROUTING_GROUP": "Key for Task List Group", "ROUTING_COUNTER": "Group Counter", "PROD_VERSION": "Production Version", "EXPLOSION_DATE": "Date of BOM Explosion/Routing Transfer", "ORDER_PRIORITY": "Order priority", "MRP_CONTROLLER": "MRP controller for the order", "EXPLODE_NEW": "New Input Values", "BUSINESS_AREA": "Business Area", "PROFIT_CENTER": "Profit Center", "SEQ_NUMBER": "Seq. number order", "STOCK_TYPE": "Stock Type", "GR_PROC_TIME": "Goods Receipt Processing Time in Days", "STORAGE_LOCATION": "Storage Location", "MRP_DISTR_KEY": "MRP Distribution Key", "GOODS_RECIPIENT": "Goods Recipient/Ship-To Party", "UNLOADING_POINT": "Unloading Point", "POSITION_NUMBER": "Order Item Number"}, "Sequences": [{"SEQUENCE": "Sequence", "BRANCH_OPER": "Number of the branch operation", "RETURN_OPER": "Number of the return operation", "TEXT": "Sequence description", "ALIGN": "Alignment key for scheduling", "DELETION_IND": "Indicator: Delete Object"}], "Operations": [{"SEQUENCE": "Sequence", "OPERATION": "Operation/Activity Number", "SUB_OPERATION": "Suboperation", "DELETION_IND": "Indicator: Delete Object", "CONTROL_KEY": "Control key", "WORK_CENTER": "Work center", "SHORT_TEXT": "Operation short text", "SEQ_NUMBER": "Sequence number operation", "BASE_QUANTITY": "Base Quantity", "STANDARD_VALUE_01": "Standard Value", "STANDARD_VALUE_01_UNIT": "Unit of measure for the standard value", "STANDARD_VALUE_02": "Standard Value", "STANDARD_VALUE_02_UNIT": "Unit of measure for the standard value", "STANDARD_VALUE_03": "Standard Value", "STANDARD_VALUE_03_UNIT": "Unit of measure for the standard value", "STANDARD_VALUE_04": "Standard Value", "STANDARD_VALUE_04_UNIT": "Unit of measure for the standard value", "STANDARD_VALUE_05": "Standard Value", "STANDARD_VALUE_05_UNIT": "Unit of measure for the standard value", "STANDARD_VALUE_06": "Standard Value", "STANDARD_VALUE_06_UNIT": "Unit of measure for the standard value", "SPLIT_REQUIRED_INDICATOR": "Required splitting", "SPLIT_MAX_NUMBER": "Maximum number of splits", "SPLIT_MIN_PROC_TIME": "Minimum processing time", "SPLIT_MIN_PROC_TIME_UNIT": "Unit for the minimum processing time", "OVERLAP_REQUIRED": "Required overlapping", "OVERLAP_OPTIONAL": "Optional overlapping", "OVERLAP_CONT_FLOW": "Indicator: continuous flow production", "OVERLAP_MIN_TIME": "Minimum overlap time", "OVERLAP_MIN_TIME_UNIT": "Unit for the minimum overlap time", "SEND_AHEAD_MIN_QUANTITY": "Minimum Send-Ahead Quantity", "PURCHASING_GROUP": "Purchasing Group", "MATERIAL_GROUP": "Material Group", "LATEST_END_EXEC_DATE": "Latest scheduled finish: Execution (date)", "LATEST_END_EXEC_TIME": "Latest scheduled finish: Execution (time)", "LATEST_START_EXEC_DATE": "Latest scheduled start: Execution (date)", "LATEST_START_EXEC_TIME": "Latest scheduled start: Execution (time)", "LATEST_START_PROC_DATE": "Latest scheduled start: Processing (date)", "LATEST_START_PROC_TIME": "Latest scheduled start: Processing (time)", "LATEST_START_TEARDOWN_DATE": "Latest scheduled start: Teardown (date)", "LATEST_START_TEARDOWN_TIME": "Latest scheduled start: Teardown (time)", "LATEST_START_WAIT_DATE": "Latest scheduled start: Wait time (date)", "LATEST_START_WAIT_TIME": "Latest scheduled start: Wait time", "LATEST_END_WAIT_DATE": "Latest scheduled finish: Wait time (date)", "LATEST_END_WAIT_TIME": "Latest scheduled finish: Wait time", "EARLIEST_END_EXEC_DATE": "Earliest scheduled finish: Execution (date)", "EARLIEST_END_EXEC_TIME": "Earliest scheduled finish: Execution (time)", "EARLIEST_START_EXEC_DATE": "Earliest scheduled start: Execution (date)", "EARLIEST_START_EXEC_TIME": "Earliest scheduled start: Execution (time)", "EARLIEST_START_PROC_DATE": "Earliest scheduled start: Processing (date)", "EARLIEST_START_PROC_TIME": "Earliest scheduled start: Processing (time)", "EARLIEST_START_TEARDOWN_DATE": "Earliest scheduled start: Teardown (date)", "EARLIEST_START_TEARDOWN_TIME": "Earliest scheduled start: Teardown (time)", "EARLIEST_START_WAIT_DATE": "Earliest scheduled start: Wait (date)", "EARLIEST_START_WAIT_TIME": "Earliest scheduled start: Wait (time)", "EARLIEST_END_WAIT_DATE": "Earliest scheduled finish: Wait (date)", "EARLIEST_END_WAIT_TIME": "Earliest scheduled finish: Wait (time)", "USER_DEFINED_KEY": "Key word ID for user-defined fields", "USER_FIELD_00_CHARACTER": "User field with 20 characters", "USER_FIELD_01_CHARACTER": "User field with 20 characters", "USER_FIELD_02_CHARACTER": "User field with 10 characters", "USER_FIELD_03_CHARACTER": "User field with 10 characters", "USER_FIELD_04_QUANTITY": "User field for quantity (length 10.3)", "USER_FIELD_04_UNIT": "User field: Unit for quantity fields", "USER_FIELD_05_QUANTITY": "User field for quantity (length 10.3)", "USER_FIELD_05_UNIT": "User field: Unit for quantity fields", "USER_FIELD_06_QUANTITY": "User-defined field for values (length 10,3)", "USER_FIELD_06_UNIT": "User field: Unit for value fields", "USER_FIELD_07_QUANTITY": "User-defined field for values (length 10,3)", "USER_FIELD_07_UNIT": "User field: Unit for value fields", "USER_FIELD_08_DATE": "User field for date", "USER_FIELD_09_DATE": "User field for date", "USER_FIELD_10_INDICATOR": "User-defined field: Indicator for reports", "USER_FIELD_11_INDICATOR": "User-defined field: Indicator for reports", "DISPATCH_INDICATOR": "Indicator: Dispatch Operation", "DEALLOCATE_INDICATOR": "Indicator: Deallocate Operation"}], "ResourcesTools": [{"SEQUENCE": "Sequence", "OPERATION": "Operation/Activity Number", "PRT_ITEM_NO": "Item Number for Production Resource/Tool", "PRT_CATEGORY": "Production resources/tools category", "PLANT": "Production resources/tools in plant", "MATERIAL": "Material Number", "MISC_PRT": "Production resources and tools", "DOCUMENT_NO": "Document number", "DOCUMENT_TYPE": "Document Type", "DOCUMENT_PART": "Document Part", "DOCUMENT_VERSION": "Document Version", "EQUIPMENT": "Equipment Number", "STEUF": "Control key for management of production resources/tools", "TEXT": "First line of text for production resources/tools", "QUANTITY": "Standard value for the PRT quantity", "QUANTITY_UOM": "Quantity unit of production resource/tool", "QUANTITY_FORMULA": "Formula for calculating the total quantity of PRT", "USAGE_VALUE": "Standard usage value for production resources/tools", "USAGE_VALUE_UOM": "Usage value unit of the production resource/tool", "USAGE_VALUE_FORMULA": "Formula for calculating the total usage value of PRT", "START_OFFSET": "Offset to start of production resource/tool usage", "START_OFFSET_UOM": "Offset unit for start of prod. resource/tool usage", "START_REF_DATE": "Reference date to start of production resource/tool usage", "END_OFFSET": "Offset to finish of production resource/tool usage", "END_OFFSET_UOM": "Offset unit for end of production resource/tool usage", "END_REF_DATE": "Reference date for end of production resource/tool usage", "DELETION_IND": "Indicator: Delete Object"}]}