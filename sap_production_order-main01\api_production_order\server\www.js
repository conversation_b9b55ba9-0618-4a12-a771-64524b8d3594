import "dotenv/config";
import http from "http";
import app from '../app/app.js';

/* Get port from environment and store in Express. */
app.set('port', process.env.PORT || '7230');

/* Create HTTP server. */
var server = http.createServer(app);

/* Event listener for HTTP server "listening" event. */
server.listen(app.get('port'), () => {
    console.log('Server listening on port ' + app.get('port'))
});