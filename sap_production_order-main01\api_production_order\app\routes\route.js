import express from 'express';
import { verifytoken } from '../auth/auth.js';
import sapppclient from "../modules/sapppclient.js";

const router = express.Router();

// Get Production Order Payload Format
router.get("/Payload", verifytoken, (req, res, next) => {
    const sapPP = new sapppclient(req);
    sapPP.GetPPOrderPayload(req.query)
        .then(retn => res.status(200).send(retn))
        .catch(error => next(error));
});

// Get Production Order Details
router.get("/Details", verifytoken, (req, res, next) => {
    const sapPP = new sapppclient(req);
    sapPP.PPOrderQueryValidation(req.query)
        .then(PPOrder => sapPP.bapi_prodord_get_details(PPOrder))
        .then(retn => res.status(200).send(retn))
        .catch(error => next(error));
});

// Update Production Order Details
router.post("/Update", verifytoken, (req, res, next) => {
    const sapPP = new sapppclient(req);
    sapPP.PPOrderUpdateValidation(req.body)
        .then(PPOrder => sapPP.bapi_prodord_update(PPOrder))
        .then(retn => res.status(200).send(retn))
        .catch(error => next(error));
});

export default router;