version: "3.9"
services:
  api_production_order:
    # container_name: api_production_order
    build: ./api_production_order
    image: api_production_order
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 2G
        reservations:
          cpus: '0.25'
          memory: 1G
    # ports:
    #   - "7230:7230"
    env_file:
      - "./config/config.env"
    environment:
      - PORT=3000
      - ASHOST=ud1-sv-sapeq1.matw.matthewsintl.com
      - SYSNR=00
      - CLIENT=300
      - LANGU=EN
      - ABAPSYS=NQ1
      - RFC_TRACE=0
      - JWTLINK=http://c3mft01ld.amer.schawk.com:8082/auth/.well-known/jwks.json
      - JWTLOGIN=http://c3mft01ld.amer.schawk.com:8082/auth/login
    networks:
      - sap_pp_network

  # Scaling up of Node-SAP API Service
  api_production_order_lb:
    container_name: api_production_order_lb
    build: ./api_production_order_lb
    image: api_production_order_lb
    restart: always
    ports:
      - "7230:3000"
    environment:
      - NGINX_PORT=3000
      - APILOADBALANCE=http://api_production_order:3000
    depends_on:
      - api_production_order
    networks:
      - sap_pp_network

networks:
  sap_pp_network: